import environment from "../../environment.js";
import axiosInstance from "../axios.js";
import idb from "../indexedDB.js";

class S3Controller {
    constructor() {
        this.cacheStore = "s3_url_cache";
        this.s3Config = {
            buckets: {
                assets: {
                    name: "quartermaster-assets",
                    region: "us-east-1",
                },
                compressedItems: {
                    name: "webapp-artifacts-compressed",
                    region: "us-east-2",
                },
            },
        };
    }

    async getCachedUrl(cacheKey) {
        try {
            const cachedItem = await idb.getItem(this.cacheStore, cacheKey);

            if (!cachedItem) {
                return null;
            }

            const now = Date.now();

            if (cachedItem.expiresAt && now >= cachedItem.expiresAt) {
                await idb.deleteItem(this.cacheStore, cacheKey);
                return null;
            }

            return cachedItem.url;
        } catch (error) {
            console.error("[S3Controller] Error getting cached URL:", error);
            return null;
        }
    }

    getExpiryFromUrl(signedUrl) {
        try {
            const url = new URL(signedUrl);
            const expires = url.searchParams.get("Expires") || url.searchParams.get("X-Amz-Expires");
            if (expires) {
                return parseInt(expires) * 1000;
            }
            return null;
        } catch (error) {
            console.error("[S3Controller] Error extracting expiry from URL:", error);
            return null;
        }
    }

    async fetchCloudfrontSignedUrl(key, bucketName) {
        try {
            const cacheKey = `signed_url_${key}`;

            const cachedUrl = await this.getCachedUrl(cacheKey);
            if (cachedUrl) {
                return cachedUrl;
            }

            const response = await axiosInstance.get("/s3/cloudfront/signedUrl", {
                params: { key: key, bucketName: bucketName },
            });

            const signedUrl = response.data.signedUrl;

            if (signedUrl) {
                const expiresAt = this.getExpiryFromUrl(signedUrl);

                if (expiresAt) {
                    await idb.addItems(this.cacheStore, [
                        {
                            _id: cacheKey,
                            url: signedUrl,
                            expiresAt: expiresAt,
                            timestamp: Date.now(),
                        },
                    ]);
                } else {
                    console.warn(`[S3Controller] Could not extract expiry from URL, not caching: ${key}`);
                }
            }

            return signedUrl;
        } catch (error) {
            console.error("Error fetching signed URL:", error);
            throw error;
        }
    }

    fetchUrl(artifact, linkType = undefined) {
        return `${environment.VITE_API_URL}/api/artifacts${linkType ? `/${linkType}` : ""}/link/${artifact._id}${linkType === "video" ? `.mp4` : ""}`;
    }

    fetchPreviewUrl(artifact) {
        return this.fetchUrl(artifact, "thumbnail_image");
    }

    async fetchCloudfrontSignedUrlBatch(batch) {
        try {
            const response = await axiosInstance.post("/s3/cloudfront/signedUrl/batch", { batch });
            return response.data.signedUrls;
        } catch (error) {
            console.error("Error fetching batch CloudFront signed URLs:", error);
            throw error;
        }
    }

    async fetchSignedUrlsBatch(artifacts) {
        try {
            const batch = [];
            const pathToArtifactMap = new Map();

            artifacts.forEach((artifact) => {
                if (artifact.thumbnail_image_path) {
                    const thumbnailKey = `webapp-artifacts-compressed:${artifact.thumbnail_image_path}`;
                    if (!pathToArtifactMap.has(thumbnailKey)) {
                        batch.push({
                            bucketName: "webapp-artifacts-compressed",
                            key: artifact.thumbnail_image_path,
                            region: "us-east-1",
                        });
                        pathToArtifactMap.set(thumbnailKey, []);
                    }
                    pathToArtifactMap.get(thumbnailKey).push({ artifactId: artifact._id, type: "thumbnail" });
                }

                if (artifact.image_path) {
                    const imageKey = `${artifact.bucket_name}:${artifact.image_path}`;
                    if (!pathToArtifactMap.has(imageKey)) {
                        batch.push({
                            bucketName: artifact.bucket_name,
                            key: artifact.image_path,
                            region: artifact.aws_region,
                        });
                        pathToArtifactMap.set(imageKey, []);
                    }
                    pathToArtifactMap.get(imageKey).push({ artifactId: artifact._id, type: "image" });
                }

                if (artifact.video_path) {
                    const videoKey = `${artifact.bucket_name}:${artifact.video_path}`;
                    if (!pathToArtifactMap.has(videoKey)) {
                        batch.push({
                            bucketName: artifact.bucket_name,
                            key: artifact.video_path,
                            region: artifact.aws_region,
                        });
                        pathToArtifactMap.set(videoKey, []);
                    }
                    pathToArtifactMap.get(videoKey).push({ artifactId: artifact._id, type: "video" });
                }
            });

            if (batch.length > 0) {
                console.log(`[S3Controller] Fetching ${batch.length} signed URLs in batch...`);
                const signedUrlsResponse = await this.fetchCloudfrontSignedUrlBatch(batch);

                const signedUrls = new Map();
                signedUrlsResponse.forEach((urlData, index) => {
                    const batchItem = batch[index];
                    const mapKey = `${batchItem.bucketName}:${batchItem.key}`;
                    const artifacts = pathToArtifactMap.get(mapKey) || [];

                    artifacts.forEach(({ artifactId, type }) => {
                        const artifactKey = `${artifactId}:${type}`;
                        signedUrls.set(artifactKey, urlData.signedUrl);
                    });
                });

                console.log(`[S3Controller] Successfully processed ${signedUrls.size} signed URLs`);
                return signedUrls;
            }

            return new Map();
        } catch (error) {
            console.error("[S3Controller] Error fetching signed URLs batch:", error);
            return new Map();
        }
    }
}
const s3Controller = new S3Controller();

export default s3Controller;
