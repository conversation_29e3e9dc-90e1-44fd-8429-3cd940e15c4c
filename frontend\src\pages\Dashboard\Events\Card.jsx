import { Grid, Typography, Tooltip } from "@mui/material";
import { memo, useMemo, useEffect, useState } from "react";
import dayjs from "dayjs";
import { displayCoordinates, permissions, userValues } from "../../../utils";
import theme from "../../../theme";
import PreviewMedia from "../../../components/PreviewMedia";
import { useUser } from "../../../hooks/UserHook.jsx";
import useVesselInfo from "../../../hooks/VesselInfoHook.jsx";

const Card = ({ card, setShowDetailModal, setSelectedCard, favouriteArtifacts, buttonsToShow, signedUrls }) => {
    const [src, setSrc] = useState(null);
    const { user } = useUser();
    const [thumbnail, setThumbnail] = useState(null);
    const { vesselInfo } = useVesselInfo();
    const hasManageArtifacts = user?.hasPermissions([permissions.manageArtifacts]);

    const roundedCoordinates = useMemo(
        () => displayCoordinates(card.location?.coordinates, !!user?.use_MGRS),
        [card.location?.coordinates, user?.use_MGRS],
    );

    const vessel = vesselInfo.find((v) => v.vessel_id === card.onboard_vessel_id);
    const vesselName = vessel.name;
    const isVideo = Boolean(card.video_path);

    const handleClick = () => {
        setShowDetailModal(true);
        setSelectedCard({
            ...card,
            vesselName,
        });
    };

    useEffect(() => {
        if (signedUrls) {
            const thumbnailUrl = signedUrls.get(`${card._id}:thumbnail`);
            const imageUrl = signedUrls.get(`${card._id}:image`);
            const videoUrl = signedUrls.get(`${card._id}:video`);

            if (isVideo) {
                setThumbnail(thumbnailUrl || imageUrl || null);
                setSrc(videoUrl || null);
            } else {
                setThumbnail(thumbnailUrl || imageUrl || null);
                setSrc(imageUrl || null);
            }
        }
    }, [card, isVideo, signedUrls]);

    if (!vesselInfo) return <Typography>No vessel info</Typography>;

    return (
        <Grid
            container
            paddingTop={"0 !important"}
            height={"100%"}
            maxHeight={"350px"}
            className={"events-step-2"}
            onClick={handleClick}
            sx={{ cursor: "pointer" }}
        >
            <Grid container backgroundColor={"primary.dark"} borderRadius={2} padding={1} gap={1}>
                <Grid size={12} maxHeight={"200px"}>
                    <PreviewMedia
                        thumbnailLink={thumbnail}
                        originalLink={src}
                        cardId={card._id}
                        isImage={!isVideo}
                        style={{ borderRadius: 8 }}
                        favouriteArtifacts={favouriteArtifacts}
                        showVideoThumbnail={isVideo}
                        onThumbnailClick={handleClick}
                        showArchiveButton={hasManageArtifacts}
                        isArchived={card?.is_archived || false}
                        vesselId={card?.onboard_vessel_id}
                        buttonsToShow={buttonsToShow}
                    />
                </Grid>
                <Grid container size={12}>
                    <Grid display={"flex"} justifyContent={"space-between"} alignItems={"center"} paddingX={1} size={12}>
                        <Tooltip title={vesselName.length > 12 ? vesselName : ""}>
                            <Typography fontSize={"14px"} fontWeight={500}>
                                {vesselName && (vesselName.length > 12 ? vesselName.slice(0, 12) + "..." : vesselName)}
                            </Typography>
                        </Tooltip>
                        <Typography fontSize={"14px"} fontWeight={500}>
                            {dayjs(card.timestamp)
                                // .tz(timezone)
                                .format(userValues.dateTimeFormat(user, { exclude_seconds: true }))}
                        </Typography>
                    </Grid>
                    <Grid display={"flex"} justifyContent={"space-between"} alignItems={"center"} paddingX={1} size={12}>
                        <Typography fontSize={"14px"} fontWeight={500} color={theme.palette.custom.mainBlue}>
                            Location
                        </Typography>
                        <Typography fontSize={"14px"} fontWeight={500} color={theme.palette.custom.mainBlue}>
                            Category
                        </Typography>
                    </Grid>
                    <Grid display={"flex"} justifyContent={"space-between"} alignItems={"center"} paddingX={1} size={12}>
                        <Typography fontSize={"14px"} fontWeight={500} maxWidth={"50%"}>
                            {roundedCoordinates}
                        </Typography>
                        <Typography fontSize={"14px"} fontWeight={500} maxWidth={"50%"} textAlign={"right"}>
                            {card.super_category
                                ? card.super_category.length > 12
                                    ? card.super_category.slice(0, 12) + "..."
                                    : card.super_category
                                : "Unspecified category"}
                        </Typography>
                    </Grid>
                </Grid>
            </Grid>
        </Grid>
    );
};

export default memo(Card);
