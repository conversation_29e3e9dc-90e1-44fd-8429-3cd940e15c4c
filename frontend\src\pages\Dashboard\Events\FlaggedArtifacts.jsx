import { useEffect, useState, useRef } from "react";
import { Grid } from "@mui/material";
import artifactFlagController from "../../../controllers/ArtifactFlag.controller";
import VirtualizedCardList from "./VirtualizedCardList";
import FlaggedCard from "./FlaggedCard";
import { getSocket } from "../../../socket";
import DetailModal from "./DetailModal";
import theme from "../../../theme";
import s3Controller from "../../../controllers/S3.controller";

const FlaggedArtifacts = () => {
    const [flaggedArtifacts, setFlaggedArtifacts] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedArtifact, setSelectedArtifact] = useState(null);
    const [showDetailModal, setShowDetailModal] = useState(false);
    const [signedUrls, setSignedUrls] = useState(new Map());
    const containerRef = useRef();
    const listRef = useRef();

    useEffect(() => {
        const fetchFlaggedArtifacts = async () => {
            try {
                setLoading(true);
                const artifacts = await artifactFlagController.getFlaggedArtifacts();
                setFlaggedArtifacts(artifacts);

                const actualArtifacts = artifacts.map((item) => item.artifact).filter(Boolean);
                const newSignedUrls = await s3Controller.fetchSignedUrlsBatch(actualArtifacts);
                setSignedUrls(newSignedUrls);
            } catch (error) {
                console.error("Error fetching flagged artifacts:", error);
            } finally {
                setLoading(false);
            }
        };

        fetchFlaggedArtifacts();
        const socket = getSocket();

        socket.on("artifacts_flagged/changed", fetchFlaggedArtifacts);
        socket.on("artifact/changed", fetchFlaggedArtifacts);

        return () => {
            socket.off("artifacts_flagged/changed", fetchFlaggedArtifacts);
            socket.off("artifact/changed", fetchFlaggedArtifacts);
        };
    }, []);

    const handleFlaggedByClick = (artifact) => {
        setSelectedArtifact(artifact);
        setShowDetailModal(true);
    };

    return (
        <Grid container color={"#FFFFFF"} flexDirection={"column"} height={"100%"}>
            <Grid
                container
                overflow={"auto"}
                display={"block"}
                border={`1px solid ${theme.palette.custom.borderColor}`}
                borderRadius={"10px"}
                padding={"10px 24px"}
                size="grow"
            >
                <Grid container height={"100%"} overflow={"auto"} ref={containerRef}>
                    <VirtualizedCardList
                        ref={listRef}
                        events={flaggedArtifacts}
                        isLoading={loading}
                        containerRef={containerRef}
                        setShowDetailModal={setShowDetailModal}
                        setSelectedCard={setSelectedArtifact}
                        favouriteArtifacts={[]}
                        CustomCard={FlaggedCard}
                        onFlaggedByClick={handleFlaggedByClick}
                        signedUrls={signedUrls}
                    />
                </Grid>
            </Grid>
            <DetailModal
                showDetailModal={showDetailModal}
                setShowDetailModal={setShowDetailModal}
                selectedCard={selectedArtifact}
                setSelectedCard={setSelectedArtifact}
                favouriteArtifacts={[]}
                signedUrls={signedUrls}
            />
        </Grid>
    );
};

export default FlaggedArtifacts;
