import React, { useEffect, useState, useRef, useMemo, useCallback } from "react";
import { Grid, Typography, Alert, useTheme, Stack, Skeleton, Box, alpha, Button } from "@mui/material";
import { CircularProgress } from "@mui/material";
import DownloadingIcon from "@mui/icons-material/Downloading";
import FiberManualRecordIcon from "@mui/icons-material/FiberManualRecord";
import ScreenshotMonitorIcon from "@mui/icons-material/ScreenshotMonitor";
import { useApp } from "../../../hooks/AppHook";
import axiosInstance from "../../../axios";
import CustomSwitch from "../../../components/CustomSwitch";
import Events from "./Events";
import Map from "./Map";
import SensorDetails from "./SensorDetails";
import Sensors from "./Sensors";
import { CUTOFF_DATE, isEnvironment, logEvent, procDownloadResponse, timezonesList } from "../../../utils";
import FloatingMenu from "../../../components/FloatingMenu";
import dayjs from "dayjs";
// import utc from "dayjs/plugin/utc";
// import timezone from "dayjs/plugin/timezone";
import Replay from "./Replay";
import VideoPlayer from "./VideoPlayer";
import gps_socket from "../../../gps_socket";
import idb from "../../../indexedDB";
import MosaicView from "./MosaicView/Mosaic";
import useGroupRegions from "../../../hooks/GroupRegionHook";
import MosaicSetting from "./MosaicView/MosaicSetting";
import environment from "../../../../environment";
import artifactController from "../../../controllers/Aritfact.controller";
import useVesselInfo from "../../../hooks/VesselInfoHook";
import { getSocket } from "../../../socket";

// dayjs.extend(utc);
// dayjs.extend(timezone);

// const menuItems = [
//     { title: "Sensors", imgSrc: "/icons/sensor-icon.svg", className: "dashboard-step-7", artifactIndicatorShow: true },
//     { title: "Map", imgSrc: "/icons/map-icon.svg", className: "dashboard-step-9" },
//     { title: "Events", imgSrc: "/icons/event-icon.svg", className: "dashboard-step-11" },
//     { title: "Replay", imgSrc: "/icons/video_icon.svg", className: "dashboard-step-5" },
//     {
//         title: "Record",
//         imgSrc: "/icons/record-icon.svg",
//         selectable: false,
//         className: "dashboard-step-14",
//         viewMode: "Single",
//         streamAvailable: true,
//         inDevMode: true,
//         tooltip:
//             "Download a 1-minute clip of the video, starting 30 seconds before you click and ending about 30 seconds after. (This might change for live video)",
//     },
//     !isEnvironment(environment.stagingAndProduction) && {
//         title: "Screenshot",
//         imgSrc: "/icons/screenshot-icon.svg",
//         selectable: false,
//         className: "dashboard-step-15",
//         viewMode: "Single",
//         streamAvailable: true,
//         inDevMode: true,
//     },
// ].filter((v) => v);

const ARTIFACT_FETCH_INTERVAL = 10 * 24 * 60 * 60 * 1000;

const VideoStream = () => {
    const theme = useTheme();
    const { isMobile, devMode, setTimezone, screenSize } = useApp();
    const { regions, fetchRegions } = useGroupRegions();

    const { vesselInfo } = useVesselInfo();
    const [showTimezone, setShowTimezone] = useState("");
    const [replayType, setReplayType] = useState("");
    const [streams, setStreams] = useState([]);
    const [streamUrl, setStreamUrl] = useState("");

    const [regionGroups, setRegionGroups] = useState([]);
    const [selectedRegionGroup, setSelectedRegionGroup] = useState("all");

    const [loadingStreams, setLoadingStreams] = useState(true);
    const [loadingHlsUrl, setLoadingHlsUrl] = useState(true);

    const [streamsError, setStreamsError] = useState("");
    const [hlsUrlError, setHlsUrlError] = useState("");

    const [selectedStream, setSelectedStream] = useState({});

    const [streamMode, setStreamMode] = useState("LIVE");
    const [activeComponentTitle, setActiveComponentTitle] = useState(null);
    const [onDemandReplay, setOnDemandReplay] = useState(60); // this is storing the replay time in minutes

    const [artifacts, setArtifacts] = useState();
    const [newCoordinate, setNewCoordinate] = useState(null);
    const [viewedArtifacts, setViewedArtifacts] = useState([]);

    const artifactsStartTimestamp = useRef(dayjs().valueOf() - ARTIFACT_FETCH_INTERVAL);
    const artifactsEndTimestamp = useRef(dayjs().valueOf());
    const [isLoadingMoreArtifacts, setIsLoadingMoreArtifacts] = useState(false);
    const [isLoadingArtifacts, setIsLoadingArtifacts] = useState(true);
    const [isDownloadDisabled, setIsDownloadDisabled] = useState(false);
    const [processingStates, setProcessingStates] = useState({
        Record: false,
        Screenshot: false,
    });

    const totalDuration = onDemandReplay * 60; // this is storing replay time in seconds
    const [scrubBarSlotInterval, setScrubBarSlotInterval] = useState(new Date().toISOString());
    const [scrubBarSlotTag, setScrubBarSlotTag] = useState("");
    const referenceTime = useRef(0);
    const referenceToTime = useRef(0);
    const downloadRef = useRef(null);
    const screenshotRef = useRef(null);
    const [playBack, setPlayBack] = useState({
        offset: 0,
        currentVideoPlayTime: 0,
        latency: 0,
    });

    const [view, setView] = useState("Single");
    const [favouriteArtifacts, setFavouriteArtifacts] = useState([]);
    const [lockSlider, setLockSlider] = useState(false);
    const [mosaicStreamOrder, setMosaicStreamOrder] = useState([]);
    const [mosaicLayout, setMosaicLayout] = useState(() => {
        if (screenSize.sm) return 2;
        if (screenSize.md) return 3;
        if (screenSize.lg) return 4;
        return 4;
    });
    const [currentIndex, setCurrentIndex] = useState(0);

    const menuItemsForDesktop = [
        ...(!isEnvironment(environment.stagingAndProduction)
            ? [
                  {
                      title: "Mosaic View",
                      imgSrc: "/icons/mosaicWhite.svg",
                  },
              ]
            : []),
        ...(view === "Mosaic"
            ? [
                  {
                      title: "Mosaic View Settings",
                      imgSrc: "/icons/mosaicsetting.svg",
                      // className: "dashboard-step-8",
                  },
              ]
            : []),
        {
            title: "Record",
            imgSrc: "/icons/record-icon.svg",
            selectable: false,
            className: "dashboard-step-14",
            viewMode: "Single",
            streamAvailable: true,
            tooltip:
                "Download a 1-minute clip of the video, starting 30 seconds before you click and ending about 30 seconds after. (This might change for live video)",
            disabled: loadingHlsUrl,
        },
        {
            title: "Screenshot",
            imgSrc: "/icons/screenshot-icon.svg",
            selectable: false,
            className: "dashboard-step-15",
            viewMode: "Single",
            streamAvailable: true,
            disabled: loadingHlsUrl,
        },
    ];
    const menuItems = [
        ...(view === "Mosaic" && isMobile
            ? [
                  {
                      title: "Mosaic View Settings",
                      imgSrc: "/icons/mosaicsetting.svg",
                      // className: "dashboard-step-8",
                  },
              ]
            : []),
        { title: "Sensors", imgSrc: "/icons/sensor-icon.svg", className: "dashboard-step-7", artifactIndicatorShow: true },
        { title: "Map", imgSrc: "/icons/map-icon.svg", className: "dashboard-step-9" },
        { title: "Events", imgSrc: "/icons/event-icon.svg", className: "dashboard-step-11" },
        { title: "Replay", imgSrc: "/icons/video_icon.svg", className: "dashboard-step-5" },
        ...(isMobile ? [] : menuItemsForDesktop),
    ].filter((v) => v);

    const changeMosaicListOrder = (newOrder) => {
        setMosaicStreamOrder(newOrder);
    };
    const changeMosaicLayout = (newLayout) => {
        setMosaicLayout(newLayout);
        setCurrentIndex(0);
    };
    const [marks, setMarks] = useState([]);
    const [marksInterval, setMarksInterval] = useState(2);

    const vessel = useMemo(() => {
        if (!selectedStream.StreamName) return {};
        return { id: selectedStream.StreamName, name: selectedStream.VesselName, vesselId: selectedStream.VesselId };
    }, [selectedStream]);

    useEffect(() => {
        // note: streams are pre-sorted by isLive
        if (!selectedStream.StreamName && streams.length > 0 && regionGroups.length > 0) {
            if (streams[0].IsLive) {
                let defaultStream = localStorage.getItem("selectedStream");
                let parsedDefaultStream = defaultStream ? JSON.parse(defaultStream) : null;
                let liveStreams = streams.filter((s) => s.IsLive);
                let selected = null;
                if (parsedDefaultStream) {
                    // Check if stored stream is still live
                    selected = liveStreams.find((s) => s.StreamName === parsedDefaultStream);
                }
                if (selected) {
                    setSelectedStream(selected);
                } else {
                    setSelectedStream(streams[0]);
                }
                updateTimezone(streams[0].RegionGroupId);
            } else {
                setStreamsError("No live streams available");
            }
        }
    }, [streams, regionGroups]);

    useEffect(() => {
        if (selectedStream.StreamName) {
            if (streamMode === "ON_DEMAND" && scrubBarSlotInterval === 0) {
                toggleStreamMode();
            } else {
                loadStreamUrl();
                // This is for showing the timezone in only custom replay section
                if (replayType === "custom") {
                    if (selectedStream && selectedStream.VesselId) {
                        const vessel = vesselInfo.find((vessel) => vessel.vessel_id === selectedStream.VesselId);
                        if (vessel && vessel.timezone) {
                            console.log("Timezone: Found timezone for artifact", vessel, vessel.timezone);
                            setShowTimezone(vessel.timezone);
                        }
                    }
                } else {
                    setShowTimezone("");
                }
                if (!isEnvironment(environment.stagingAndProduction)) {
                    loadMarksOnStream();
                }
            }
        }
    }, [streamMode, selectedStream, onDemandReplay, scrubBarSlotInterval, view]);

    useEffect(() => {
        setLoadingStreams(true);
        fetchStreams();
        setSelectedStream({});
        setHlsUrlError("");
        setView("Single");
    }, []);

    useEffect(() => {
        if (!streams) return;
        fetchRegionGroups();
        setMosaicStreamOrder(streams.filter((stream) => stream.IsLive));
    }, [streams, regions]);

    useEffect(() => {
        artifactsStartTimestamp.current = dayjs().valueOf() - ARTIFACT_FETCH_INTERVAL;
        artifactsEndTimestamp.current = dayjs().valueOf();
        setArtifacts([]);
        setFavouriteArtifacts([]);
    }, [selectedStream]);
    useEffect(() => {
        if (!selectedStream.StreamName) return;
        updateTimezone(selectedStream.RegionGroupId);
        if (!selectedStream.VesselId) {
            setArtifacts([]);
            return;
        } else {
            setArtifacts();
        }
        setIsLoadingArtifacts(true);
        axiosInstance
            .post(`/v2/artifacts/` + selectedStream.VesselId, {
                startTimestamp: artifactsStartTimestamp.current,
                endTimestamp: artifactsEndTimestamp.current,
                favourites: 1,
            })
            .then(async (res) => {
                if (res.data.artifacts.length > 0) {
                    setFavouriteArtifacts(res.data.favouritesArtifacts);
                    setArtifacts(res.data.artifacts);
                    return;
                } else {
                    fetchArtifactsRecursively(
                        artifactsStartTimestamp.current - ARTIFACT_FETCH_INTERVAL,
                        artifactsEndTimestamp.current - ARTIFACT_FETCH_INTERVAL,
                    );
                }
            })
            .catch(console.error)
            .finally(() => setIsLoadingArtifacts(false));
    }, [selectedStream]);

    useEffect(() => {
        const socket = getSocket();
        const handleArtifactChanged = (data) => {
            const updatedArtifact = data?.artifact;
            if (!updatedArtifact) return;
            setArtifacts((prev) => {
                if (!prev) return prev;
                if (updatedArtifact.is_archived) {
                    return prev.filter((a) => a._id !== updatedArtifact._id);
                } else {
                    const newEvents = [...prev, updatedArtifact];
                    return newEvents.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
                }
            });
        };
        socket.on("artifact/changed", handleArtifactChanged);
        return () => socket.off("artifact/changed", handleArtifactChanged);
    }, [selectedStream]);

    const updateTimezone = (regionGroupId) => {
        const regionGroup = regionGroups.find((rg) => rg._id === regionGroupId);
        if (!regionGroup) return console.warn("[updateTimezone] Region group not found", regionGroupId);

        const { representative } = timezonesList.find((tz) => tz.offset === regionGroup.timezone);
        if (!representative) return console.warn("[updateTimezone] Representative timezone not found", regionGroup.timezone);

        setTimezone(representative);
    };

    const fetchArtifactsRecursively = async (startTimestamp, endTimestamp) => {
        setIsLoadingArtifacts(true);
        const fetched = await fetchArtifacts(startTimestamp, endTimestamp);
        if (startTimestamp < CUTOFF_DATE) {
            setIsLoadingArtifacts(false);
            setFavouriteArtifacts([]);
            return setArtifacts([]);
        }

        if (fetched.artifacts.length > 0) {
            setArtifacts(fetched.artifacts);
            setFavouriteArtifacts(fetched.favouritesArtifacts);
            setIsLoadingArtifacts(false);
            artifactsStartTimestamp.current = startTimestamp;
            artifactsEndTimestamp.current = endTimestamp;
        } else {
            fetchArtifactsRecursively(startTimestamp - ARTIFACT_FETCH_INTERVAL, endTimestamp - ARTIFACT_FETCH_INTERVAL);
        }
    };

    const fetchArtifacts = (startTimestamp, endTimestamp) => {
        return new Promise((resolve, reject) => {
            if (!selectedStream.StreamName) return;
            axiosInstance
                .post(`/v2/artifacts/` + selectedStream.VesselId, { startTimestamp: startTimestamp, endTimestamp: endTimestamp, favourites: 1 })
                .then((res) => resolve(res.data))
                .catch((err) => reject(err));
        });
    };

    const fetchMoreArtifacts = useCallback(() => {
        return new Promise((resolve, reject) => {
            setIsLoadingMoreArtifacts(true);
            artifactsStartTimestamp.current -= ARTIFACT_FETCH_INTERVAL;
            artifactsEndTimestamp.current -= ARTIFACT_FETCH_INTERVAL;
            if (!selectedStream.StreamName) return;
            axiosInstance
                .post(`/v2/artifacts/` + selectedStream.VesselId, {
                    startTimestamp: artifactsStartTimestamp.current,
                    endTimestamp: artifactsEndTimestamp.current,
                    favourites: 1,
                })
                .then((res) => resolve(res.data))
                .catch((err) => reject(err))
                .finally(() => setIsLoadingMoreArtifacts(false));
        });
    }, [selectedStream.StreamName]);

    useEffect(() => {
        if (vessel) {
            fetchVesselCoordinates(vessel.vesselId);
        }
    }, [vessel]);

    const fetchVesselCoordinates = (vesselId) => {
        if (!vesselId) return;
        axiosInstance
            .post("/v2/vesselLocations/" + vesselId, { lastKnown: 1 })
            .then((res) => {
                if (res.data.latitude && res.data.longitude) {
                    setNewCoordinate(
                        res.data.latitude
                            ? {
                                  ...res.data,
                                  lat: res.data.latitude,
                                  lng: res.data.longitude,
                              }
                            : {},
                    );
                }
            })
            .catch(console.error);
    };

    const fetchRegionGroups = async () => {
        if (regions) {
            const filteredRegionGroups = regions.filter((rg) => streams.some((s) => s.RegionGroupId === rg._id));
            setRegionGroups(filteredRegionGroups);
        } else {
            fetchRegions();
        }
    };

    const fetchStreams = async () => {
        setStreamsError("");
        try {
            let streamList = await axiosInstance
                .get(`/kinesis/listStreams`)
                .then((res) => res.data.filter((stream) => (devMode ? true : stream.VesselIsActive)));
            if (streamList.length === 0) {
                setStreamsError("No streams found.");
            } else {
                setStreamsError("");
            }
            setStreams(streamList.sort((a, b) => (a.IsLive && !b.IsLive ? -1 : 1)));
        } catch (err) {
            console.error("error in axios", err);
            setStreamsError("Failed to fetch streams. Please try again later. " + err?.response?.data?.message);
            setStreams([]);
        }
        setLoadingStreams(false);
    };

    const loadStreamUrl = async () => {
        setHlsUrlError("");
        console.log("loading...");
        setLoadingHlsUrl(true);
        try {
            // note: have to fetch new url everytime. cannot restream with same url
            const { url, minutes } = await axiosInstance
                .get(
                    `/v2/kinesis/dashStreamingSessionURL?${new URLSearchParams({ region: selectedStream.Region, streamName: selectedStream.StreamName, streamMode, startTimestamp: scrubBarSlotInterval, totalDuration: onDemandReplay }).toString()}`,
                    { meta: { showSnackbar: false } },
                )
                .then((res) => res.data.data);
            setStreamUrl(url);
            logEvent("StreamUrlLoaded", { streamName: selectedStream.StreamName, streamMode, url });
            // Set scrub bar slider position based on percentage
            if (minutes) {
                setPlayBack(() => ({
                    offset: playBack.offset + minutes * 60,
                    currentVideoPlayTime: 0,
                    latency: 0,
                }));
            }
        } catch (err) {
            console.log("error in loadStreamUrl", err);
            setStreamUrl("");
            const data = err?.response?.data;
            setHlsUrlError(data?.message || `An unexpected error occurred. Please try again later. ${err?.message}`);
            logEvent("StreamUrlLoadFailed", { streamName: selectedStream.StreamName, streamMode, error: err?.message });
        } finally {
            const refTime = referenceToTime.current && referenceToTime.current !== 0 ? referenceToTime.current : dayjs();
            referenceTime.current = dayjs(refTime).valueOf();
        }
        setLoadingHlsUrl(false);
    };

    const scrubbarToggle = () => {
        setLockSlider(!lockSlider);
    };
    const loadMarksOnStream = async () => {
        try {
            const endTimestamp = Date.now();
            const startTimestamp = Date.now() - totalDuration * 1000;
            const marks = await artifactController.getArtifactTimeSeries(startTimestamp, endTimestamp, marksInterval);
            if (marks.status === 200) {
                setMarks(marks.data.filter((mark) => mark.count > 0));
            }
        } catch (error) {
            console.error("Error fetching marks on stream:", error);
        }
    };

    const toggleMosaicView = () => {
        logEvent("ToggleView", { from: view, to: view === "Single" ? "Mosaic" : "Single" });
        if (view === "Single") {
            setView("Mosaic");
        } else if (view === "Mosaic") {
            setView("Single");
        }
        // resetPlayer();
        // loadStreamUrl();
    };
    const toggleStreamMode = () => {
        logEvent("ToggleStreamMode", {
            mode: streamMode === "LIVE" ? "ON_DEMAND" : "LIVE",
            duration: streamMode === "LIVE" ? scrubBarSlotInterval : 60,
        });
        if (streamMode === "LIVE") {
            setStreamMode("ON_DEMAND");
            setOnDemandReplay(60);
            setScrubBarSlotInterval(new Date().toISOString());
            setMarksInterval(2);
        } else if (streamMode === "ON_DEMAND") setStreamMode("LIVE");
    };

    const downloadMedia = async (url) => {
        await axiosInstance
            .get(url, {
                responseType: "blob",
                timeout: 120000, // 2 minutes
            })
            .then(procDownloadResponse);
    };

    const buildDownloadMediaArgs = () => {
        const args = {
            streamName: selectedStream.StreamName,
            region: selectedStream.Region,
        };

        if (streamMode === "LIVE") {
            args.timestamp = referenceTime.current + playBack.currentVideoPlayTime * 1000 - playBack.latency * 500;
        } else {
            // for recorded stream
            const timeOffset = (-onDemandReplay * 60 + playBack.offset + playBack.currentVideoPlayTime) * 1000;
            args.timestamp = referenceTime.current + timeOffset;
        }

        // return new URLSearchParams(args);
        return args;
    };
    const isValidTimestamp = (timestamp) => {
        return typeof timestamp === "number" && !isNaN(timestamp) && isFinite(timestamp);
    };

    const downloadVideoPart = async () => {
        setProcessingStates((prev) => ({ ...prev, Record: true }));

        try {
            const args = buildDownloadMediaArgs();
            if (!isValidTimestamp(args.timestamp)) {
                setProcessingStates((prev) => ({ ...prev, Record: false }));
                return;
            }
            logEvent("DownloadVideoClip", { ...args });
            await downloadMedia(`/kinesis/getClip?${new URLSearchParams(args).toString()}`);
        } catch (err) {
            console.error("error in axios", err);
        } finally {
            setProcessingStates((prev) => ({ ...prev, Record: false }));
        }
    };
    const downloadVideoPart2 = async (isChecked) => {
        if (isChecked) {
            return;
        }

        setIsDownloadDisabled(true);
        downloadRef.current && downloadRef.current.setDisabled(true);

        try {
            const args = buildDownloadMediaArgs();
            if (!isValidTimestamp(args.timestamp)) {
                downloadRef.current && downloadRef.current.setDisabled(false);
                setIsDownloadDisabled(false);
                return;
            }
            logEvent("DownloadVideoClip", { ...args });
            await downloadMedia(`/kinesis/getClip?${new URLSearchParams(args).toString()}`);
        } catch (err) {
            console.error("error in axios", err);
        } finally {
            console.log("finally");
            downloadRef.current && downloadRef.current.setDisabled(false);
            downloadRef.current && downloadRef.current.toggle();
            setIsDownloadDisabled(false);
        }
    };

    const downloadScreenshot = async () => {
        setProcessingStates((prev) => ({ ...prev, Screenshot: true }));

        try {
            const args = buildDownloadMediaArgs();
            if (!isValidTimestamp(args.timestamp)) {
                setProcessingStates((prev) => ({ ...prev, Screenshot: false }));
                return;
            }
            logEvent("DownloadScreenshot", { ...args });
            await downloadMedia(`/kinesis/getScreenShot?${new URLSearchParams(args).toString()}`);
        } catch (err) {
            console.error("error in axios", err);
        } finally {
            setProcessingStates((prev) => ({ ...prev, Screenshot: false }));
        }
    };
    const downloadScreenshot2 = async (isChecked) => {
        if (isChecked) {
            return;
        }

        setIsDownloadDisabled(true);
        screenshotRef.current && screenshotRef.current.setDisabled(true);

        try {
            const args = buildDownloadMediaArgs();
            if (!isValidTimestamp(args.timestamp)) {
                screenshotRef.current && screenshotRef.current.setDisabled(false);
                setIsDownloadDisabled(false);
                return;
            }
            logEvent("DownloadScreenshot", { ...args });
            await downloadMedia(`/kinesis/getScreenShot?${new URLSearchParams(args).toString()}`);
        } catch (err) {
            console.error("error in axios", err);
        } finally {
            console.log("finally");
            screenshotRef.current && screenshotRef.current.setDisabled(false);
            screenshotRef.current && screenshotRef.current.toggle();
            setIsDownloadDisabled(false);
        }
    };

    const handleReplayTime = (replay) => {
        setStreamMode("ON_DEMAND");
        setOnDemandReplay(replay.interval);
        setScrubBarSlotInterval(replay.time);
        referenceToTime.current = replay?.toDate || 0;
        setScrubBarSlotTag(replay.tag);
        setMarksInterval(replay.marksDuartion);
        setReplayType(replay?.type || "");
        // console.log("handleReplayTime:", "time", currentVideoPlayTime)
        setPlayBack({
            offset: 0,
            currentVideoPlayTime: 0,
        });
    };

    const handleMenuUpdate = (title) => {
        setActiveComponentTitle(title);

        if (title === "Record") {
            downloadVideoPart();
        }

        if (title === "Screenshot") {
            downloadScreenshot();
        }
    };

    const [artifactIndicator, setArtifactIndicator] = useState({});
    const getArtifactIndicator = async () => {
        if (streams.length === 0) return;
        const currentTimestamp = Date.now();
        const thirtyMinutesAgo = currentTimestamp - 30 * 60 * 1000; // Last 30 minutes
        try {
            const filteredArtifactIndicator = {};
            const vesselIds = streams.filter((s) => s.VesselId).map((s) => s.VesselId);
            if (vesselIds.length === 0) return;
            const response = await axiosInstance.post("/artifacts/activityIndicators/bulk", {
                vesselIds,
                startTimestamp: thirtyMinutesAgo,
                endTimestamp: currentTimestamp,
            });
            const api_artifacts = response.data.latestArtifacts || [];
            const local_artifacts = {};
            await Promise.all(
                vesselIds.map(async (vesselId) => {
                    const store = `${vesselId}_artifact`;
                    const filter = (item) => dayjs(item.timestamp).isBetween(thirtyMinutesAgo, currentTimestamp);
                    const dbData = await idb.getItems(store, filter);
                    local_artifacts[vesselId] = dbData || [];
                }),
            );
            api_artifacts.forEach((vessel) => {
                const apiArtifacts = vessel.artifacts || [];
                const localArtifacts = local_artifacts[vessel._id] || [];

                const filteredArtifacts = apiArtifacts.filter((apiArtifact) => {
                    const localArtifact = localArtifacts.find((local) => local._id === apiArtifact._id);

                    if (!localArtifact) {
                        return true;
                    }
                    if (localArtifact.isView === true) {
                        return false;
                    }
                    return true;
                });

                if (filteredArtifacts.length > 0) {
                    filteredArtifactIndicator[vessel._id] = filteredArtifacts;
                }
            });
            setArtifactIndicator(filteredArtifactIndicator);
        } catch (error) {
            console.error("Error fetching artifact indicators:", error);
        }
    };

    const updateArtifactIndicator = async (artifact) => {
        console.log("Indicator: updated", artifact);
        if (!artifact || !artifact.onboard_vessel_id) return;
        const vesselId = artifact.onboard_vessel_id;
        setArtifactIndicator((prev) => {
            const prevArtifacts = prev[vesselId] || [];
            const updatedArtifacts = [
                ...prevArtifacts.filter((a) => a._id !== artifact._id), // if the artifact already exist then we only update it otherwise we add it
                artifact,
            ];
            return {
                ...prev,
                [vesselId]: updatedArtifacts,
            };
        });
    };

    useEffect(() => {
        gps_socket.on("artifactDetected", updateArtifactIndicator);
        return () => {
            gps_socket.off("artifactDetected", updateArtifactIndicator);
        };
    }, []);

    useEffect(() => {
        getArtifactIndicator();
    }, [streams]);

    const memoizedArtifactIndicator = useMemo(() => artifactIndicator, [artifactIndicator]);
    const memoizedViewedArtifacts = useMemo(() => viewedArtifacts, [viewedArtifacts]);
    const memoizedFavouriteArtifacts = useMemo(() => favouriteArtifacts, [favouriteArtifacts]);

    const componentMap = {
        Map: (
            <Grid
                container
                className="dashboard-step-10"
                height={{ xs: 200, lg: 300 }}
                width={{ xs: "100%", lg: 400 }}
                sx={{
                    backgroundColor: theme.palette.primary.light,
                }}
            >
                {!selectedStream.StreamName ? (
                    <Grid width={"100%"}>
                        <Skeleton animation="wave" variant="rectangular" height={"100%"} />
                    </Grid>
                ) : (
                    <Grid width={{ xs: "100%", lg: 400 }}>
                        <Map vessel={vessel} newCoordinate={newCoordinate} />
                    </Grid>
                )}
            </Grid>
        ),
        Events: (
            <Events
                isLoadingArtifacts={isLoadingArtifacts}
                loadedArtifacts={artifacts}
                setLoadedArtifacts={setArtifacts}
                fetchMoreArtifacts={fetchMoreArtifacts}
                isLoadingMoreArtifacts={isLoadingMoreArtifacts}
                selectedStream={selectedStream}
                artifactIndicator={memoizedArtifactIndicator}
                viewedArtifacts={memoizedViewedArtifacts}
                setViewedArtifacts={setViewedArtifacts}
                setFavouriteArtifacts={setFavouriteArtifacts}
                favouriteArtifacts={memoizedFavouriteArtifacts}
                setArtifactIndicator={setArtifactIndicator}
            />
        ),
        Sensors: (
            <Sensors
                loadingStreams={loadingStreams}
                view={view}
                streams={streams}
                selectedStream={selectedStream}
                setStreamMode={setStreamMode}
                setSelectedStream={setSelectedStream}
                fetchStreams={fetchStreams}
                artifactIndicator={artifactIndicator}
                viewedArtifacts={viewedArtifacts}
                regionGroups={regionGroups}
                selectedRegionGroup={selectedRegionGroup}
                setSelectedRegionGroup={setSelectedRegionGroup}
            />
        ),
        Replay: (
            <Replay
                toggleStreamMode={toggleStreamMode}
                mode={streamMode}
                handleReplayTime={handleReplayTime}
                loadingHlsUrl={loadingHlsUrl}
                onMenuUpdate={handleMenuUpdate}
                selectedStream={selectedStream}
            />
        ),
        Record: (
            <Grid size={{ xs: true }} display={"flex"} justifyContent={"center"}>
                {!processingStates.Record && (
                    <Button
                        variant="contained"
                        onClick={downloadVideoPart}
                        fullWidth
                        sx={{
                            fontSize: "12px",
                            padding: "5px 10px",
                            borderRadius: "4px",
                        }}
                        disabled={loadingHlsUrl}
                    >
                        Download Video Clip
                    </Button>
                )}
                {processingStates.Record && <CircularProgress sx={{ color: "white" }} />}
            </Grid>
        ),
        "Mosaic View Settings": (
            <MosaicSetting
                lockSlider={lockSlider}
                scrubbarToggle={scrubbarToggle}
                streams={mosaicStreamOrder}
                changeMosaicListOrder={changeMosaicListOrder}
                mosaicLayout={mosaicLayout}
                changeMosaicLayout={changeMosaicLayout}
            />
        ),
    };

    // Add a logEvent when a new stream is selected
    useEffect(() => {
        if (selectedStream && selectedStream.StreamName) {
            localStorage.setItem("selectedStream", JSON.stringify(selectedStream.StreamName));

            logEvent("StreamSelected", {
                streamName: selectedStream.StreamName,
                vesselName: selectedStream.VesselName,
                region: selectedStream.Region,
            });
        }
    }, [selectedStream.StreamName]);

    return (
        <Grid container sx={{ overflow: "auto", height: "100%", width: "100%" }} flexDirection={{ xs: "row", lg: "column" }}>
            <Grid container size={{ xs: 12, lg: true }} flexDirection={"column"} position={"relative"} height={{ xs: "auto", lg: "100%" }}>
                <Grid container size={"auto"} flexDirection={"row"} alignItems={"center"} color={"#FFFFFF"} padding={2} minHeight={"72px"}>
                    <Grid container size={{ xs: true }} justifyContent={"space-between"} alignItems={"center"} width={"100%"}>
                        {loadingHlsUrl ? (
                            <>
                                <Grid>
                                    <Stack
                                        display={"flex"}
                                        alignItems={{ xs: "flex-start", lg: "flex-start" }}
                                        gap={{ xs: 0, lg: "20px" }}
                                        flexDirection={{ xs: "column", lg: "row" }}
                                    >
                                        <Skeleton variant="text" width={200} />
                                        <Skeleton variant="text" width={40} />
                                    </Stack>
                                </Grid>
                            </>
                        ) : (
                            <>
                                <Grid container size={{ xs: "auto" }} flexDirection={"column"} gap={0.5}>
                                    <Grid
                                        display={"flex"}
                                        alignItems={{ xs: "flex-start", lg: "center" }}
                                        gap={{ xs: 0, lg: "20px" }}
                                        flexDirection={{ xs: "column", lg: "row" }}
                                    >
                                        {/*<Grid className="dashboard-step-8">*/}
                                        {/*    <Typography sx={{ fontSize: { xs: "12px", sm: "18px" }, width: "auto" }}>*/}
                                        {/*        {view === "Mosaic"*/}
                                        {/*            ? "Mosaic View"*/}
                                        {/*            : (!devMode && selectedStream.VesselName) || selectedStream.StreamName || "Sensor Name"}*/}
                                        {/*    </Typography>*/}
                                        {/*</Grid>*/}
                                        <Grid className="dashboard-step-3">
                                            <Typography sx={{ fontSize: { xs: "12px", sm: "18px" }, width: "auto" }}>
                                                {view === "Mosaic"
                                                    ? "Mosaic View"
                                                    : (!devMode && selectedStream.VesselName) || selectedStream.StreamName || "Sensor Name"}
                                            </Typography>
                                        </Grid>
                                        <Grid className="dashboard-step-4">
                                            <Box display={"flex"} size={{ xs: "auto" }} alignItems={"center"} gap={0.4}>
                                                <FiberManualRecordIcon
                                                    sx={{
                                                        color:
                                                            selectedStream.IsLive && streamMode === "LIVE"
                                                                ? theme.palette.custom.live
                                                                : theme.palette.custom.replay,
                                                        fontSize: { xs: "10px", md: "18px" },
                                                    }}
                                                />
                                                <Typography
                                                    sx={{
                                                        color:
                                                            selectedStream.IsLive && streamMode === "LIVE"
                                                                ? theme.palette.custom.live
                                                                : theme.palette.custom.offline,
                                                    }}
                                                    fontSize={{ xs: "10px", md: "16px" }}
                                                    fontWeight={400}
                                                >
                                                    {selectedStream.IsLive && streamMode === "LIVE"
                                                        ? "LIVE"
                                                        : streamMode === "ON_DEMAND"
                                                          ? `REPLAY (${scrubBarSlotTag}) ${showTimezone}`
                                                          : "OFFLINE"}
                                                </Typography>
                                            </Box>
                                        </Grid>
                                    </Grid>
                                </Grid>
                                <Grid container justifyContent={"flex-end"} gap={1} flexDirection={"row"} size="grow" width={"100%"}>
                                    {isMobile && (
                                        <Grid
                                            display={selectedStream.StreamName && view === "Single" ? "flex" : "none"}
                                            className="dashboard-step-15"
                                        >
                                            <CustomSwitch
                                                ref={screenshotRef}
                                                initialState={false}
                                                textOff="Take a screenshot"
                                                textOn="Preparing..."
                                                iconOff={<ScreenshotMonitorIcon fontSize="medium" color="primary" />}
                                                iconOn={<CircularProgress />}
                                                icon={true}
                                                action={downloadScreenshot2}
                                                disabled={isDownloadDisabled}
                                            />
                                        </Grid>
                                    )}

                                    {isMobile && (
                                        <Grid
                                            display={selectedStream.StreamName && view === "Single" ? "flex" : "none"}
                                            className="dashboard-step-14"
                                        >
                                            <CustomSwitch
                                                ref={downloadRef}
                                                initialState={false}
                                                textOff="Download video's part"
                                                textOn="Downloading..."
                                                iconOff={<DownloadingIcon fontSize="medium" color="primary" />}
                                                iconOn={<CircularProgress />}
                                                icon={true}
                                                action={downloadVideoPart2}
                                                disabled={isDownloadDisabled}
                                                tooltip={
                                                    "Click and get the zip with part of the current video which covers 1 minute length: 30 seconds before click and ~30 after (can be different for live streams)"
                                                }
                                            />
                                        </Grid>
                                    )}
                                    {isMobile && !isEnvironment(environment.stagingAndProduction) && (
                                        <Grid
                                            // display={selectedStream.StreamName && view === "Single" ? "flex" : "none"}
                                            display={selectedStream.StreamName ? "flex" : "none"}
                                            className="dashboard-step-16"
                                        >
                                            <CustomSwitch
                                                initialState={view === "Single"}
                                                textOff="Show Single View"
                                                textOn="Show Mosaic View"
                                                iconOff={"/icons/mosaic.svg"}
                                                iconOn={"/icons/mosaic.svg"}
                                                action={toggleMosaicView}
                                            />
                                        </Grid>
                                    )}
                                    <Grid display={selectedStream.StreamName && streamMode !== "LIVE" ? "flex" : "none"} className="dashboard-step-5">
                                        <CustomSwitch
                                            initialState={streamMode !== "LIVE"}
                                            textOff="Show Replay"
                                            textOn="Show Live"
                                            iconOff={"/replay-icon.svg"}
                                            iconOn={"/live-icon.svg"}
                                            action={toggleStreamMode}
                                        />
                                    </Grid>
                                    {/* <Grid display={isEnvironment(environment.stagingAndProduction) ? "none" : "flex"}>
                                        <CustomSwitch
                                            initialState={view === "Single"}
                                            textOff="Show Single View"
                                            textOn="Show Mosaic View"
                                            iconOff={<VignetteOutlinedIcon fontSize="20" />}
                                            iconOn={<BorderAllRoundedIcon fontSize="20" color="black" sx={{ backgroundColor: "black" }} />}
                                            icon={true}
                                            action={toggleMosaicView}
                                        />
                                    </Grid> */}
                                </Grid>
                            </>
                        )}
                    </Grid>
                </Grid>
                {view === "Single" ? (
                    <Grid size={"grow"}>
                        <VideoPlayer
                            setStreamUrl={setStreamUrl}
                            setPlayBack={setPlayBack}
                            playBack={playBack}
                            streamUrl={streamUrl}
                            streamMode={streamMode}
                            totalDuration={totalDuration}
                            setScrubBarSlotInterval={setScrubBarSlotInterval}
                            selectedStream={selectedStream}
                            referenceTime={referenceTime}
                            handleReplay={handleReplayTime}
                            hlsUrlError={hlsUrlError}
                            marks={marks}
                        />
                    </Grid>
                ) : (
                    <Grid size={"grow"}>
                        <MosaicView
                            streams={mosaicStreamOrder}
                            setPlayBack={setPlayBack}
                            playBack={playBack}
                            streamUrl={streamUrl}
                            setStreamUrl={setStreamUrl}
                            streamMode={streamMode}
                            totalDuration={totalDuration}
                            setScrubBarSlotInterval={setScrubBarSlotInterval}
                            scrubBarSlotInterval={scrubBarSlotInterval}
                            selectedStream={selectedStream}
                            referenceTime={referenceTime}
                            scrubbarToggle={scrubbarToggle}
                            lockSlider={lockSlider}
                            mosaicLayout={mosaicLayout}
                            currentIndex={currentIndex}
                            setCurrentIndex={setCurrentIndex}
                        />
                    </Grid>
                )}
            </Grid>
            <Grid
                size={{ xs: 12, lg: "auto" }}
                sx={{ position: "absolute", bottom: 70, left: isMobile ? 0 : 55, zIndex: 10, display: streamsError ? "flex" : "none" }}
            >
                <Alert severity="error">{streamsError}</Alert>
            </Grid>
            <Grid
                size={{ xs: 12, lg: "auto" }}
                sx={{ position: "absolute", bottom: 45, left: isMobile ? 0 : 55, zIndex: 10, display: hlsUrlError ? "flex" : "none" }}
            >
                <Alert severity="error">{hlsUrlError}</Alert>
            </Grid>
            <Grid
                size={{ xs: 12 }}
                className="mobile-step-6"
                display={isMobile && view === "Mosaic" ? "none" : { xs: "flex", lg: "none" }}
                flexDirection={"column"}
                gap={2}
                paddingTop={4}
                paddingX={1.2}
                sx={{
                    backgroundColor: theme.palette.custom.darkBlue,
                    paddingBottom: 1.2,
                }}
            >
                <Grid>
                    <Typography fontSize={"16px"} fontWeight={"600"} color={"#FFFFFF"}>
                        Sensor Details
                    </Typography>
                </Grid>
                <Grid
                    sx={{
                        backgroundColor: alpha(theme.palette.primary.light, 0.5),
                        borderRadius: "10px",
                        padding: "15px 20px",
                    }}
                >
                    <SensorDetails selectedStream={selectedStream} />
                </Grid>
            </Grid>
            <FloatingMenu
                menuItems={menuItems.filter(
                    (itm) =>
                        (!itm.viewMode || itm.viewMode === view) &&
                        (itm.streamAvailable === undefined || itm.streamAvailable === !!selectedStream) &&
                        (itm.inDevMode === undefined || itm.inDevMode === devMode),
                )}
                activeComponentTitle={activeComponentTitle}
                onMenuUpdate={handleMenuUpdate}
                withPadding={false}
                withMinContent={true}
                toggle={activeComponentTitle !== "Record" && activeComponentTitle !== "Screenshot"}
                artifactIndicator={artifactIndicator}
                componentMap={componentMap}
                isProcessing={processingStates}
                mosaicToggle={toggleMosaicView}
                view={view}
            >
                {!isMobile
                    ? componentMap[activeComponentTitle] || null
                    : menuItems.map((item, index) => (
                          <Grid
                              key={index}
                              className={item.className}
                              container
                              display={{ xs: "flex", lg: "none" }}
                              flexDirection={"column"}
                              gap={2}
                              paddingTop={4}
                              paddingX={1.2}
                              sx={{
                                  backgroundColor: theme.palette.custom.darkBlue,
                                  paddingBottom: 1.2,
                                  position: "relative",
                                  maxWidth: "calc(100vw - 10px)",
                              }}
                          >
                              <Grid size={{ xs: 12 }}>
                                  <Typography fontSize={"16px"} fontWeight={"600"} color={"#FFFFFF"}>
                                      {item.title}
                                  </Typography>
                              </Grid>
                              <Grid
                                  size={{ xs: 12 }}
                                  sx={{
                                      borderRadius: "10px",
                                      overflow: "hidden",
                                  }}
                              >
                                  {componentMap[item.title]}
                              </Grid>
                          </Grid>
                      ))}
            </FloatingMenu>
        </Grid>
    );
};

export default VideoStream;
