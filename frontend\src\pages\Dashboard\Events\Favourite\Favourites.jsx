import { Grid } from "@mui/material";
import { useEffect, useState, memo, useRef } from "react";
import DetailModal from "../DetailModal";
import { useParams } from "react-router-dom";
import theme from "../../../../theme";
import { getSocket } from "../../../../socket";
import VirtualizedCardList from "../VirtualizedCardList";
import favouriteArtifactsController from "../../../../controllers/FavouriteArtifacts.controller";
import s3Controller from "../../../../controllers/S3.controller";

const Favourites = ({ vessels }) => {
    const { id } = useParams();
    const [events, setEvents] = useState([]);
    const [filteredEvents, setFilteredEvents] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [showDetailModal, setShowDetailModal] = useState(false);
    const [selectedCard, setSelectedCard] = useState(null);
    const [favouriteArtifacts, setFavouriteArtifacts] = useState([]);
    const [signedUrls, setSignedUrls] = useState(new Map());
    const virtualizedListContainerRef = useRef();

    const fetchArtifacts = async (isSocketTriggered = false) => {
        if (!isSocketTriggered) {
            setIsLoading(true);
        }
        try {
            const { artifacts, favourites } = await favouriteArtifactsController.getUserFavouriteArtifacts();

            setFavouriteArtifacts(favourites);
            setEvents(artifacts);

            const newSignedUrls = await s3Controller.fetchSignedUrlsBatch(artifacts);
            setSignedUrls(newSignedUrls);

            setIsLoading(false);
        } catch (err) {
            setIsLoading(false);
            console.error(`Error fetching favourites artifacts in Events`, err);
        }
    };

    useEffect(() => {
        const unidIds = vessels.map((v) => v.unit_id);
        setFilteredEvents(events.filter((e) => unidIds.includes(e.unit_id)));
    }, [events, vessels]);

    useEffect(() => {
        fetchArtifacts();
    }, [id]);

    useEffect(() => {
        const socket = getSocket();
        const handleArtifactChanged = (data) => {
            const updatedArtifact = data?.artifact;
            if (!updatedArtifact) return;
            setEvents((prev) => {
                if (updatedArtifact.is_archived) {
                    return prev.filter((a) => a._id !== updatedArtifact._id);
                } else {
                    const newEvents = [...prev, updatedArtifact];
                    return newEvents.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
                }
            });
        };
        socket.on("favourites/changed", () => fetchArtifacts(true));
        socket.on("artifact/changed", handleArtifactChanged);

        return () => {
            socket.off("favourites/changed", () => fetchArtifacts(true));
            socket.off("artifact/changed", handleArtifactChanged);
        };
    }, []);

    return (
        <Grid container color={"#FFFFFF"} flexDirection={"column"} height={"100%"}>
            <Grid
                container
                overflow={"auto"}
                display={"block"}
                border={`1px solid ${theme.palette.custom.borderColor}`}
                borderRadius={"10px"}
                padding={"10px 24px"}
                size="grow"
            >
                <Grid container height={"100%"} overflow={"auto"} ref={virtualizedListContainerRef}>
                    <VirtualizedCardList
                        events={filteredEvents}
                        setShowDetailModal={setShowDetailModal}
                        setSelectedCard={setSelectedCard}
                        favouriteArtifacts={favouriteArtifacts}
                        isLoading={isLoading}
                        containerRef={virtualizedListContainerRef}
                        signedUrls={signedUrls}
                    />
                </Grid>
            </Grid>
            <DetailModal
                showDetailModal={showDetailModal}
                setShowDetailModal={setShowDetailModal}
                selectedCard={selectedCard}
                setSelectedCard={setSelectedCard}
                id={id}
                favouriteArtifacts={favouriteArtifacts}
                signedUrls={signedUrls}
            />
        </Grid>
    );
};

export default memo(Favourites);
