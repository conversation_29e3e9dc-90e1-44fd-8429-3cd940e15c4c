<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <link rel="icon" type="image/png" href="/favicon.ico">
        <link href="https://fonts.googleapis.com/css2?family=Lexend:wght@400;700&display=swap" rel="stylesheet">
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Outfit:wght@100..900&display=swap" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Nunito+Sans:ital,opsz,wght@0,6..12,200..1000;1,6..12,200..1000&display=swap" rel="stylesheet">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Quartermaster Web App</title>
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-N0QMBKZ3LX"></script>
        <script>
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());

          gtag('config', 'G-N0QMBKZ3LX'); //this is a dev key
        </script>
        <!-- This is for microsoft clarity -->
         <script type="text/javascript">
            (function(c,l,a,r,i,t,y){
                c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
            })(window, document, "clarity", "script", "sbaw0nicou");
        </script>
        <style>
    /* Style the splash screen */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    #splash {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      /* position: fixed; */
      width: 100vw;
      height: 100vh;
      background-color: #f8f8f8;
      color: #333;
      font-size: 24px;
      line-height: normal;
      font-weight: bold;
      font-family: Arial, sans-serif;
    }

    /* Add a rotation animation to the image */
    @keyframes rotate {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    #splash img {
      animation: rotate 2s linear infinite;
      /* Rotate image continuously */
    }

    #splash p {
      margin-top: 10px;
    }
        </style>
    </head>
    <body>
        <div id="splash">
            <img src="/quartermaster-logo-dark.svg" width="100px">
            <p>Loading...</p>
        </div>
        <div id="root"></div>
        <script type="module" src="/src/main.jsx"></script>
    </body>
</html>
